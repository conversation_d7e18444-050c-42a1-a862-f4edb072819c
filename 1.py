import os
import shutil

def clear_augment_ai_auth_cache():
    # مسارات التخزين الخاصة بـ VSCode
    base_dirs = [
        os.path.join(os.environ['APPDATA'], 'Code', 'User', 'globalStorage'),
        os.path.join(os.environ['APPDATA'], 'Code', 'User', 'workspaceStorage')
    ]

    # كلمات مفتاحية للعثور على ملفات الجلسة
    auth_keywords = ['auth', 'session', 'cache', 'token']

    removed_files = []

    for base in base_dirs:
        if not os.path.exists(base):
            continue

        for root, dirs, files in os.walk(base):
            if 'augment' in root.lower():
                for file in files:
                    if any(keyword in file.lower() for keyword in auth_keywords):
                        file_path = os.path.join(root, file)
                        try:
                            os.remove(file_path)
                            removed_files.append(file_path)
                        except Exception as e:
                            print(f"[!] فشل حذف الملف {file_path}: {e}")

    if removed_files:
        print("\n✅ تم حذف ملفات تسجيل الدخول التالية بنجاح:\n")
        for f in removed_files:
            print(" -", f)
    else:
        print("ℹ️ لم يتم العثور على ملفات جلسة Augment AI لحذفها.")

if __name__ == "__main__":
    clear_augment_ai_auth_cache()