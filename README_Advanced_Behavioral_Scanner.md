# 🔒 نظام الفحص المتقدم والسلوكي للثغرات الأمنية

## 📋 نظرة عامة

نظام احترافي متطور لاكتشاف الثغرات الأمنية مع تقارير مفصلة على مستوى احترافي يمكن قبوله في منصات مثل HackerOne و Bugcrowd.

## ✨ الميزات الجديدة المضافة

### 🎯 تقارير مفصلة لكل ثغرة منفصلة
- تقرير JSON و HTML لكل ثغرة مكتشفة
- معلومات شاملة تشمل:
  - نوع الثغرة وطريقة اكتشافها
  - الأدلة والتأثير على الموقع
  - موقع الثغرة (الرابط، الوسم، المعلمة)
  - إثبات التنفيذ (PoC) واضح ومفصل
  - توصيات الإصلاح والمراجع

### 📸 التقاط صور احترافي
- صور قبل وبعد الاختبار لكل ثغرة
- التحقق من التأثير الفعلي قبل التقاط الصورة النهائية
- عدم حفظ الصور إلا بعد تأكيد ظهور التأثير المرئي
- تسمية منظمة للصور مع معلومات مفصلة

### 🔄 نظام الاستئناف المتقدم
- حفظ حالة الفحص تلقائياً كل 5 روابط
- إمكانية استئناف الفحص من آخر نقطة توقف
- حفظ النتائج والتقدم في ملف JSON
- استرداد كامل للحالة عند الاستئناف

### 🖥️ واجهة CLI بسيطة وفعالة
- قائمة خيارات واضحة ومنظمة
- دعم كامل للغة العربية
- معالجة أخطاء شاملة
- تجربة مستخدم محسنة

## 🚀 كيفية الاستخدام

### تشغيل النظام
```bash
python "advanced_and_behavioral_scan (1).py"
```

### خيارات القائمة الرئيسية
```
[1] بدء فحص جديد
[2] استئناف الفحص من آخر رابط تم إيقافه  
[3] الخروج من الأداة
```

## 📁 هيكل المجلدات والتقارير

### مجلدات النتائج
```
reports/
├── individual_vulnerabilities_YYYYMMDD_HHMMSS/
│   ├── vuln_001_XSS/
│   │   ├── vulnerability_report_001.json
│   │   ├── vulnerability_report_001.html
│   │   ├── before_test_001.png
│   │   └── after_test_001.png
│   ├── vuln_002_SQL_Injection/
│   │   ├── vulnerability_report_002.json
│   │   ├── vulnerability_report_002.html
│   │   ├── before_test_002.png
│   │   └── after_test_002.png
│   └── ...
├── comprehensive_scan_YYYYMMDD_HHMMSS/
│   ├── comprehensive_report.json
│   └── comprehensive_report.html
└── ...
```

### ملفات الحالة
```
scan_state.json  # ملف حفظ حالة الفحص للاستئناف
```

## 🔍 تفاصيل التقارير المفصلة

### محتويات تقرير JSON لكل ثغرة
```json
{
  "vulnerability_id": "VULN-001",
  "discovery_timestamp": "2024-01-01T12:00:00",
  "target_information": {
    "primary_target": "https://example.com",
    "affected_url": "https://example.com/page",
    "scan_type": "advanced_scan"
  },
  "vulnerability_details": {
    "type": "XSS",
    "severity": "High",
    "confidence": 0.9,
    "verified": true,
    "description": "Cross-Site Scripting vulnerability",
    "cwe_id": "CWE-79",
    "cvss_score": 7.5
  },
  "discovery_method": {
    "detection_technique": "Advanced automated scanning",
    "payload_used": "<script>alert('XSS')</script>",
    "verification_method": "Automated Detection"
  },
  "evidence": {
    "raw_response": "Response containing payload",
    "response_headers": {},
    "status_code": 200,
    "patterns_matched": ["<script>", "alert"]
  },
  "impact_analysis": {
    "confidentiality": "Medium",
    "integrity": "High", 
    "availability": "Low",
    "business_impact": "Session hijacking, data theft",
    "overall_risk": "High Risk",
    "exploitation_complexity": "Low - Direct payload execution"
  },
  "location_details": {
    "url": "https://example.com/page",
    "domain": "example.com",
    "path": "/page",
    "affected_parameter": "search",
    "http_method": "GET",
    "injection_point": "URL parameter"
  },
  "proof_of_concept": {
    "description": "Proof of Concept for XSS vulnerability",
    "steps_to_reproduce": [...],
    "curl_command": "curl -X GET \"https://example.com/page?search=<script>alert('XSS')</script>\"",
    "browser_reproduction": [...],
    "expected_result": "JavaScript alert dialog should appear",
    "actual_result": "Alert executed successfully",
    "screenshots": [...]
  },
  "remediation": {
    "recommendations": [
      "Implement proper input validation and output encoding",
      "Use Content Security Policy (CSP) headers"
    ],
    "priority": "High (P1)",
    "estimated_effort": "Low (1-2 days)"
  },
  "references": [
    "https://owasp.org/www-community/attacks/xss/",
    "https://cwe.mitre.org/data/definitions/79.html"
  ]
}
```

## 🛡️ أنواع الثغرات المدعومة

### الفحص المتقدم
- Cross-Site Scripting (XSS)
- SQL Injection
- Insecure Direct Object References (IDOR)
- Local File Inclusion (LFI)
- Remote File Inclusion (RFI)
- Command Injection
- Path Traversal
- Server-Side Request Forgery (SSRF)
- XML External Entity (XXE)
- Cross-Site Request Forgery (CSRF)

### التحليل السلوكي
- شذوذات في الاستجابة
- تحليل الأداء والسرعة
- كشف الحماية والـ Fingerprinting
- مراقبة الشبكة
- تحليل التفاعل البشري

## 📊 مستوى التقارير الاحترافي

### معايير HackerOne/Bugcrowd
- ✅ وصف مفصل للثغرة
- ✅ خطوات التكرار الواضحة
- ✅ إثبات التنفيذ (PoC) عملي
- ✅ تحليل التأثير والمخاطر
- ✅ صور توضيحية عالية الجودة
- ✅ توصيات الإصلاح المحددة
- ✅ مراجع تقنية موثوقة
- ✅ تصنيف CVSS و CWE

## ⚙️ المتطلبات التقنية

### المكتبات الأساسية
```bash
pip install selenium requests lxml beautifulsoup4
pip install webdriver-manager  # اختياري
pip install fake-useragent     # اختياري
pip install scikit-learn numpy # اختياري للتعلم الآلي
```

### متطلبات إضافية
- Chrome/Chromium browser
- ChromeDriver (يتم تحميله تلقائياً مع webdriver-manager)

## 🔧 الإعدادات المتقدمة

### تخصيص عمق الزحف
```python
scanner = AdvancedBehavioralScanner(max_depth=5)  # افتراضي: 3
```

### تخصيص عدد العمال
```python
scanner = AdvancedBehavioralScanner(max_workers=20)  # افتراضي: CPU cores * 4
```

## 📝 ملاحظات مهمة

### الاستخدام الأخلاقي
- استخدم هذا النظام فقط على المواقع التي تملكها أو لديك إذن صريح لفحصها
- احترم قوانين الأمن السيبراني المحلية والدولية
- لا تستخدم النتائج لأغراض ضارة

### الأداء والكفاءة
- يتم حفظ التقدم تلقائياً كل 5 روابط
- يمكن إيقاف الفحص بـ Ctrl+C واستئنافه لاحقاً
- النظام محسن للتعامل مع المواقع الكبيرة

### جودة التقارير
- جميع التقارير تتبع معايير الصناعة
- الصور تُلتقط فقط عند تأكيد التأثير الفعلي
- التقارير جاهزة للتقديم في منصات Bug Bounty

## 🆘 استكشاف الأخطاء

### مشاكل شائعة وحلولها
1. **خطأ ChromeDriver**: تأكد من تثبيت Chrome وwebdriver-manager
2. **مشاكل الشبكة**: تحقق من الاتصال بالإنترنت وإعدادات الجدار الناري
3. **نفاد الذاكرة**: قلل عدد العمال أو عمق الزحف
4. **فشل الاستئناف**: تحقق من وجود ملف scan_state.json

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع ملفات السجل في مجلد `logs/`
- تحقق من ملف `advanced_scan.log` للتفاصيل التقنية
- تأكد من تحديث جميع المكتبات المطلوبة

---

**تم تطوير هذا النظام لأغراض الأمن السيبراني الأخلاقي والتعليمي فقط**
