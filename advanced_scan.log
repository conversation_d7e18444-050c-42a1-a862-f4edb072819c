2025-06-02 23:33:23,743 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:23,758 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:23,790 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:23,881 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:23,894 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:23,961 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,421 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,438 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,454 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,888 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,906 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,971 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:29,020 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:29,122 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:29,140 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:29,214 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:29,371 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:31,521 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /w3css/tryit.asp?filename=tryw3css_ref_badges
2025-06-02 23:33:36,142 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/python_lists_add.asp
2025-06-02 23:33:37,734 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /angular/angular_ref_directives.asp
2025-06-02 23:33:39,877 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/python_ml_train_test.asp
2025-06-02 23:33:43,003 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:43,888 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /mysql/mysql_groupby.asp
2025-06-02 23:33:45,502 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/python_ref_set.asp
2025-06-02 23:33:49,335 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_variables_identifiers1
2025-06-02 23:33:49,885 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_variables_multiple1
2025-06-02 23:33:49,885 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_algorithms1
2025-06-02 23:33:56,300 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /colors/colors_mixer.asp?colorbottom=B22222&colortop=FFFFFF
2025-06-02 23:33:58,135 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/python_primitivetypes.asp
2025-06-02 23:33:58,939 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/pandas/exercise.asp?x=xrcise_cleaning_empty_cells1
2025-06-02 23:33:58,941 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /mysql/trymysql.asp?filename=trysql_op_in
2025-06-02 23:34:00,701 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/trypython.asp?filename=demo_tuple_loop
2025-06-02 23:34:00,722 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/sql_server.asp
2025-06-02 23:34:00,809 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/showpython.asp?filename=demo_mysql_inner_join
2025-06-02 23:34:00,966 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:00,998 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:01,068 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:01,079 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:01,201 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:02,135 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/trysql.asp?filename=trysql_op_greater_than
2025-06-02 23:34:04,898 - WARNING - Some characters could not be decoded, and were replaced with REPLACEMENT CHARACTER.
2025-06-02 23:34:05,083 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/ref_keyword_and.asp
2025-06-02 23:34:05,607 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:05,623 - WARNING - Some characters could not be decoded, and were replaced with REPLACEMENT CHARACTER.
2025-06-02 23:34:05,635 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:05,748 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:05,884 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:05,932 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:05,999 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /git/git_exercises.asp
2025-06-02 23:34:06,141 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/func_sqlserver_datefromparts.asp
2025-06-02 23:34:06,318 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,438 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,771 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,773 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,824 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,834 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,839 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,150 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,267 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,550 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /colors/colors_mixer.asp?colorbottom=483D8B&colortop=FFFFFF
2025-06-02 23:34:07,617 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,619 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,750 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,767 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,802 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:08,210 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/func_sqlserver_radians.asp
2025-06-02 23:34:09,338 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /charsets/ref_emoji.asp
2025-06-02 23:34:09,519 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /plus/index.php
2025-06-02 23:34:09,784 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/exercise.asp?x=xrcise_strings_modify1
2025-06-02 23:34:09,959 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/exercise.asp?x=xrcise_modules1
2025-06-02 23:34:11,966 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_strings_length1
2025-06-02 23:34:14,984 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,085 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,216 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,217 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,257 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,430 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,447 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,461 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,581 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,582 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,584 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,797 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,966 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,968 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:17,115 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:17,184 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:17,184 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:17,311 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:18,182 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/cpp_getstarted.asp
2025-06-02 23:34:18,732 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/cpp_conditions_reallife.asp
2025-06-02 23:34:19,832 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/trypython.asp?filename=demo_list_comprehension_hello
2025-06-02 23:34:20,261 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_data_types_numeric1
2025-06-02 23:34:21,663 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /tags/tag_div.asp
2025-06-02 23:34:21,734 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:21,780 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:21,867 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:21,869 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:21,883 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,152 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /react/react_es6_array_methods.asp
2025-06-02 23:34:22,186 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,315 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,332 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,333 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,349 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,366 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,433 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,548 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,632 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,832 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,880 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,900 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,900 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,998 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:23,015 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:23,035 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/exercise.asp?x=xrcise_count1
2025-06-02 23:34:23,100 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:23,166 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:24,364 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/trycpp.asp?filename=demo_stack_size
2025-06-02 23:34:24,547 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:24,665 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /plus/index.php
2025-06-02 23:34:24,797 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,000 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,016 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,147 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,197 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,198 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,312 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,312 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,548 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:26,164 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/trypython.asp?filename=demo_matplotlib_scatter_cmap_OrRd
2025-06-02 23:34:26,289 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:26,383 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:26,383 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:26,564 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:26,916 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /lib/pathfinder/main.css?v=1.0.7
2025-06-02 23:34:27,031 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:27,033 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:27,062 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:27,211 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:28,434 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/func_sqlserver_quotename.asp
2025-06-02 23:34:28,965 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:28,966 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,095 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,215 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,367 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,596 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,835 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,864 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,932 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,934 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,934 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:30,034 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:30,035 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /howto/howto_js_sort_numeric_array.asp
2025-06-02 23:34:31,911 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:31,930 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:31,932 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:31,966 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:31,980 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:31,983 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:32,015 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:32,018 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:32,312 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/cpp_getstarted.asp
2025-06-02 23:34:32,594 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/trypython.asp?filename=demo_list_comprehension_hello
2025-06-02 23:34:34,229 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_arrays1
2025-06-02 23:34:43,460 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /charsets/ref_utf_math.asp
2025-06-02 23:34:43,561 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:43,581 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:44,051 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:44,246 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:44,661 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:47,729 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /w3js/w3js_selectors.asp
2025-06-02 23:34:48,645 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:48,862 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:48,912 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:48,928 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:49,002 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:49,002 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:51,278 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:51,375 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:51,387 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:51,391 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /howto/howto_js_sort_numeric_array.asp
2025-06-02 23:34:52,660 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:52,694 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:52,729 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:52,827 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:53,012 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:53,210 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,476 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,476 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,529 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,562 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,681 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,683 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,695 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,725 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,725 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,726 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,732 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,744 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,776 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,882 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,894 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,895 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,895 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,921 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,927 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,928 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,930 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,934 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:57,011 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:57,413 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /css/css_study_plan.asp
2025-06-02 23:34:59,721 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_operators_logical1
2025-06-02 23:35:00,232 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,244 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,244 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,626 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,630 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,644 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,645 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,792 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,794 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,873 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,896 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,094 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,275 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,362 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,371 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,495 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,496 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,511 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,511 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,626 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,679 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,713 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,769 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,878 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,910 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,609 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,628 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,794 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,795 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,811 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,813 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,977 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,027 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,177 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,554 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,558 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,561 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,643 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,694 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:04,175 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:04,175 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:04,412 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:04,944 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:04,960 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,111 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,127 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,496 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,709 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /mysql/func_mysql_mod.asp
2025-06-02 23:35:05,890 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,890 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,959 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:06,046 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:06,280 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:06,293 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /howto/howto_js_sort_numeric_array.asp
2025-06-02 23:35:07,245 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:07,277 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:07,376 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:08,426 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:09,131 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:15,616 - WARNING - Some characters could not be decoded, and were replaced with REPLACEMENT CHARACTER.
2025-06-02 23:35:31,789 - WARNING - Some characters could not be decoded, and were replaced with REPLACEMENT CHARACTER.
2025-06-02 23:35:50,261 - WARNING - Some characters could not be decoded, and were replaced with REPLACEMENT CHARACTER.
2025-06-02 23:41:29,230 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-02 23:41:29,240 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-02 23:42:44,926 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_detailed_error_messages'
2025-06-02 23:42:55,427 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_debug_features_exposure'
2025-06-02 23:44:35,745 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-02 23:44:35,751 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-02 23:45:19,893 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_detailed_error_messages'
2025-06-02 23:45:22,351 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_debug_features_exposure'
2025-06-02 23:46:16,491 - ERROR - خطأ في تطبيق K-Means: name 'KMeans' is not defined
2025-06-02 23:47:25,391 - ERROR - خطأ في التقاط الصور المتقدمة: Message: timeout: Timed out receiving message from renderer: 15.000
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF6996564EC]
	(No symbol) [0x00007FF6996561DA]
	(No symbol) [0x00007FF699653E8A]
	(No symbol) [0x00007FF69965483F]
	(No symbol) [0x00007FF6996633AE]
	(No symbol) [0x00007FF6996797E1]
	(No symbol) [0x00007FF69968091A]
	(No symbol) [0x00007FF699654FAD]
	(No symbol) [0x00007FF6996794D0]
	(No symbol) [0x00007FF69970F276]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-02 23:48:14,786 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-02 23:48:14,930 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-02 23:49:00,122 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_detailed_error_messages'
2025-06-02 23:49:02,502 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_debug_features_exposure'
2025-06-02 23:49:37,223 - ERROR - خطأ في تطبيق K-Means: name 'KMeans' is not defined
2025-06-02 23:50:21,392 - ERROR - خطأ في التقاط الصور المتقدمة: Message: timeout: Timed out receiving message from renderer: 8.000
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF6996564EC]
	(No symbol) [0x00007FF6996561DA]
	(No symbol) [0x00007FF699653E8A]
	(No symbol) [0x00007FF69965483F]
	(No symbol) [0x00007FF6996633AE]
	(No symbol) [0x00007FF6996797E1]
	(No symbol) [0x00007FF69968091A]
	(No symbol) [0x00007FF699654FAD]
	(No symbol) [0x00007FF6996794D0]
	(No symbol) [0x00007FF69970F276]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-02 23:51:56,504 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-02 23:51:56,512 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-02 23:54:33,645 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_authentication_logic_bypass'
2025-06-02 23:54:33,654 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_redirect_uri_vulnerabilities'
2025-06-02 23:54:51,230 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cp
2025-06-02 23:55:35,284 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-02 23:55:38,584 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
2025-06-02 23:57:16,948 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_authentication_logic_bypass'
2025-06-02 23:57:17,017 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_redirect_uri_vulnerabilities'
2025-06-02 23:57:54,564 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-02 23:57:57,095 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
2025-06-02 23:59:39,291 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_authentication_logic_bypass'
2025-06-02 23:59:39,300 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_redirect_uri_vulnerabilities'
2025-06-03 00:14:07,241 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_special_authentication_conditions'
2025-06-03 00:14:07,256 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_state_vulnerabilities'
2025-06-03 00:14:49,596 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_unsafe_default_settings'
2025-06-03 00:23:21,667 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_authentication_logic_bypass'
2025-06-03 00:23:21,679 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-03 00:24:06,849 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-03 00:24:09,779 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
2025-06-03 00:33:37,180 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-03 00:33:37,190 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-03 00:34:22,100 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-03 00:34:25,175 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
2025-06-03 00:36:03,467 - ERROR - خطأ في التقاط الصور المتقدمة: Message: session not created
from unknown error: unable to discover open pages
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF6996AC0B8]
	(No symbol) [0x00007FF6996A6518]
	(No symbol) [0x00007FF6996A1ADD]
	(No symbol) [0x00007FF6996F52D8]
	(No symbol) [0x00007FF6996F48B0]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-03 00:36:03,892 - ERROR - خطأ في إنشاء التقارير المفصلة للثغرات: 'str' object has no attribute 'get'
2025-06-03 00:36:30,619 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-03 00:36:30,818 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-03 00:37:12,463 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-03 00:37:15,359 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
2025-06-03 00:37:56,814 - ERROR - خطأ في التقاط الصور المتقدمة: Message: unknown error: unable to discover open window in chrome
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF699640AA0]
	(No symbol) [0x00007FF6996F5441]
	(No symbol) [0x00007FF6996F48B0]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-03 00:37:56,819 - ERROR - خطأ في إنشاء التقارير المفصلة للثغرات: 'str' object has no attribute 'get'
2025-06-03 00:38:20,099 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-03 00:38:20,393 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-03 00:38:49,179 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-03 00:38:51,670 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
2025-06-03 00:49:38,023 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 00:49:38,025 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 00:49:39,094 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 00:49:39,106 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 00:50:28,950 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-03 00:50:28,951 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-03 00:50:59,138 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-03 00:51:01,360 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
2025-06-03 00:51:45,031 - INFO - ====== WebDriver manager ======
2025-06-03 00:51:47,383 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:51:48,377 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:51:48,656 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:51:49,320 - INFO - WebDriver version 136.0.7103.113 selected
2025-06-03 00:51:49,327 - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/136.0.7103.113/win32/chromedriver-win32.zip
2025-06-03 00:51:49,327 - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/136.0.7103.113/win32/chromedriver-win32.zip
2025-06-03 00:51:49,679 - INFO - Driver downloading response is 200
2025-06-03 00:52:22,925 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:52:25,124 - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113]
2025-06-03 00:53:42,794 - ERROR - خطأ في التقاط الصور المتقدمة: Message: session not created
from unknown error: unable to discover open pages
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF6996AC0B8]
	(No symbol) [0x00007FF6996A6518]
	(No symbol) [0x00007FF6996A1ADD]
	(No symbol) [0x00007FF6996F52D8]
	(No symbol) [0x00007FF6996F48B0]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-03 00:53:46,887 - INFO - ====== WebDriver manager ======
2025-06-03 00:53:49,585 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:53:53,000 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:53:53,253 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:54:01,009 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:54:01,016 - INFO - ====== WebDriver manager ======
2025-06-03 00:54:03,929 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:54:08,326 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:54:08,601 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:54:20,057 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:54:20,077 - INFO - ====== WebDriver manager ======
2025-06-03 00:54:23,432 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:54:27,741 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:54:28,021 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:54:32,606 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:54:32,650 - INFO - ====== WebDriver manager ======
2025-06-03 00:54:35,692 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:54:40,023 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:54:40,375 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:54:45,254 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:54:45,260 - INFO - ====== WebDriver manager ======
2025-06-03 00:54:48,789 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:54:53,204 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:54:53,454 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:55:00,951 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:55:00,958 - INFO - ====== WebDriver manager ======
2025-06-03 00:55:03,067 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:55:07,387 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:55:07,661 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:55:16,416 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:55:16,438 - INFO - ====== WebDriver manager ======
2025-06-03 00:55:21,346 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:55:25,668 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:55:25,945 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:55:33,698 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:55:33,707 - INFO - ====== WebDriver manager ======
2025-06-03 00:55:36,413 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:55:40,733 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:55:41,012 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:55:50,897 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:55:50,905 - INFO - ====== WebDriver manager ======
2025-06-03 00:55:54,025 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:55:58,332 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:55:58,701 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:56:06,394 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:56:06,417 - INFO - ====== WebDriver manager ======
2025-06-03 00:56:09,316 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:56:13,662 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:56:14,007 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:56:22,459 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:56:22,467 - INFO - ====== WebDriver manager ======
2025-06-03 00:56:25,267 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:56:29,609 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:56:29,919 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:56:50,206 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:57:43,953 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-03 00:57:43,955 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-03 00:58:15,589 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-03 00:58:17,937 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
2025-06-03 00:58:56,655 - INFO - ====== WebDriver manager ======
2025-06-03 00:58:59,645 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:59:04,044 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:59:04,421 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:59:17,441 - ERROR - خطأ في التقاط الصور المتقدمة: Message: unknown error: unable to discover open window in chrome
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF699640AA0]
	(No symbol) [0x00007FF6996F5441]
	(No symbol) [0x00007FF6996F48B0]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-03 00:59:21,994 - INFO - ====== WebDriver manager ======
2025-06-03 00:59:25,603 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:59:28,643 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:59:28,928 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:59:34,038 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:59:34,060 - INFO - ====== WebDriver manager ======
2025-06-03 00:59:38,433 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:59:42,871 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:59:43,131 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 00:59:49,703 - ERROR - خطأ في إنشاء متصفح التقاط الصور: Message: unknown error: unable to discover open window in chrome
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EEEBD]
	(No symbol) [0x0018905F]
	(No symbol) [0x0018880A]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 00:59:53,776 - INFO - ====== WebDriver manager ======
2025-06-03 00:59:56,359 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:59:56,620 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 00:59:56,903 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:00:04,469 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:00:04,523 - INFO - ====== WebDriver manager ======
2025-06-03 01:00:07,416 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:00:13,194 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:00:13,554 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:00:25,199 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:00:25,207 - INFO - ====== WebDriver manager ======
2025-06-03 01:00:27,906 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:00:32,286 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:00:32,637 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:00:39,681 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:00:39,692 - INFO - ====== WebDriver manager ======
2025-06-03 01:00:42,619 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:00:46,917 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:00:47,200 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:01:50,089 - ERROR - خطأ في إنشاء متصفح التقاط الصور: Message: session not created
from unknown error: unable to discover open pages
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x00149411]
	(No symbol) [0x00144017]
	(No symbol) [0x0013F6C9]
	(No symbol) [0x00188DF3]
	(No symbol) [0x0018880A]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:01:54,160 - INFO - ====== WebDriver manager ======
2025-06-03 01:01:55,920 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:01:56,195 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:01:56,451 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:02:04,706 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:02:04,712 - INFO - ====== WebDriver manager ======
2025-06-03 01:02:07,311 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:02:11,636 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:02:12,640 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:02:19,903 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:02:19,907 - INFO - ====== WebDriver manager ======
2025-06-03 01:02:22,309 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:02:26,722 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:02:26,997 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:02:34,269 - ERROR - خطأ في إنشاء متصفح التقاط الصور: Message: unknown error: unable to discover open window in chrome
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EEEBD]
	(No symbol) [0x0018905F]
	(No symbol) [0x0018880A]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:02:38,565 - INFO - ====== WebDriver manager ======
2025-06-03 01:02:52,159 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:02:52,421 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:02:52,807 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:03:02,748 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:03:02,966 - INFO - ====== WebDriver manager ======
2025-06-03 01:03:06,134 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:03:10,485 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:03:10,749 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:03:15,881 - ERROR - خطأ في التقاط صور إثبات الثغرة: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EFBC9]
	(No symbol) [0x00183CBE]
	(No symbol) [0x0019DF19]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:03:58,260 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-03 01:03:58,261 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-03 01:04:29,300 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-03 01:04:31,483 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
2025-06-03 01:05:16,141 - INFO - ====== WebDriver manager ======
2025-06-03 01:05:18,205 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:05:22,526 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:05:22,852 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:05:30,783 - ERROR - خطأ في التقاط الصور المتقدمة: Message: unknown error: unable to discover open window in chrome
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF699640AA0]
	(No symbol) [0x00007FF6996F5441]
	(No symbol) [0x00007FF6996F48B0]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-03 01:05:34,845 - INFO - ====== WebDriver manager ======
2025-06-03 01:05:38,208 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:13:47,537 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 01:13:47,543 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 01:13:47,954 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 01:14:31,234 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_special_authentication_conditions'
2025-06-03 01:15:15,319 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_unsafe_default_settings'
2025-06-03 01:15:26,297 - ERROR - خطأ في اختبار الأنماط السلوكية المتقدم: 'AdvancedBehavioralScanner' object has no attribute '_infer_ui_logic_with_ai'
2025-06-03 01:16:14,259 - INFO - ====== WebDriver manager ======
2025-06-03 01:16:17,124 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:16:17,404 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:16:17,656 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:16:36,876 - ERROR - خطأ في التقاط الصور المتقدمة: Message: unknown error: unable to discover open window in chrome
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF699640AA0]
	(No symbol) [0x00007FF6996F5441]
	(No symbol) [0x00007FF6996F48B0]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-03 01:16:40,940 - INFO - ====== WebDriver manager ======
2025-06-03 01:16:43,858 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:16:47,826 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:16:48,163 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:16:54,524 - ERROR - خطأ في إنشاء متصفح التقاط الصور: Message: unknown error: unable to discover open window in chrome
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EEEBD]
	(No symbol) [0x0018905F]
	(No symbol) [0x0018880A]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:16:58,703 - INFO - ====== WebDriver manager ======
2025-06-03 01:17:05,181 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:17:05,476 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:17:05,876 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:17:19,448 - INFO - ====== WebDriver manager ======
2025-06-03 01:17:22,259 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:17:26,706 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:17:27,057 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:17:37,625 - INFO - ====== WebDriver manager ======
2025-06-03 01:17:40,641 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:17:45,009 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:17:45,272 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:17:54,333 - INFO - ====== WebDriver manager ======
2025-06-03 01:17:57,558 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:18:01,903 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:18:02,244 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:19:05,062 - ERROR - خطأ في إنشاء متصفح التقاط الصور: Message: session not created
from unknown error: unable to discover open pages
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x00149411]
	(No symbol) [0x00144017]
	(No symbol) [0x0013F6C9]
	(No symbol) [0x00188DF3]
	(No symbol) [0x0018880A]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:19:09,180 - INFO - ====== WebDriver manager ======
2025-06-03 01:19:11,321 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:19:11,625 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:19:11,893 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:19:20,982 - INFO - ====== WebDriver manager ======
2025-06-03 01:19:24,359 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:19:28,710 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:19:28,999 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:19:40,228 - INFO - ====== WebDriver manager ======
2025-06-03 01:19:43,751 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:19:48,175 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:19:48,444 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:20:52,016 - ERROR - خطأ في إنشاء متصفح التقاط الصور: Message: session not created
from unknown error: unable to discover open pages
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x00149411]
	(No symbol) [0x00144017]
	(No symbol) [0x0013F6C9]
	(No symbol) [0x00188DF3]
	(No symbol) [0x0018880A]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:20:56,086 - INFO - ====== WebDriver manager ======
2025-06-03 01:20:59,105 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:20:59,469 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:20:59,798 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:21:08,835 - INFO - ====== WebDriver manager ======
2025-06-03 01:21:11,392 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:21:15,827 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:21:16,207 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:21:25,534 - INFO - ====== WebDriver manager ======
2025-06-03 01:21:28,473 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:21:32,935 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:21:33,346 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:21:41,106 - ERROR - خطأ في إنشاء متصفح التقاط الصور: Message: unknown error: unable to discover open window in chrome
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EEEBD]
	(No symbol) [0x0018905F]
	(No symbol) [0x0018880A]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:21:45,211 - INFO - ====== WebDriver manager ======
2025-06-03 01:21:47,425 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:21:47,719 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:21:47,974 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:21:57,095 - INFO - ====== WebDriver manager ======
2025-06-03 01:22:00,267 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:22:04,606 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:22:04,874 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:22:10,372 - ERROR - خطأ في إنشاء متصفح التقاط الصور: Message: unknown error: unable to discover open window in chrome
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x002EFC03+61635]
	GetHandleVerifier [0x002EFC44+61700]
	(No symbol) [0x001105D3]
	(No symbol) [0x000EEEBD]
	(No symbol) [0x0018905F]
	(No symbol) [0x0018880A]
	(No symbol) [0x0017D096]
	(No symbol) [0x0014C840]
	(No symbol) [0x0014D6A4]
	GetHandleVerifier [0x00574523+2701795]
	GetHandleVerifier [0x0056FCA6+2683238]
	GetHandleVerifier [0x0058A9EE+2793134]
	GetHandleVerifier [0x003068C5+155013]
	GetHandleVerifier [0x0030CFAD+181357]
	GetHandleVerifier [0x002F7458+92440]
	GetHandleVerifier [0x002F7600+92864]
	GetHandleVerifier [0x002E1FF0+5296]
	BaseThreadInitThunk [0x75725D49+25]
	RtlInitializeExceptionChain [0x76EDD03B+107]
	RtlGetAppContainerNamedObjectPath [0x76EDCFC1+561]
	(No symbol) [0x00000000]

2025-06-03 01:35:55,599 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000028F6EAE9100>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed')': /
2025-06-03 01:36:00,300 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000028F70793EC0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed')': /
2025-06-03 01:36:07,006 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000028F707A8200>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed')': /
2025-06-03 01:41:28,071 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 01:41:28,071 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 01:41:28,477 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 01:41:28,483 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 01:41:28,483 - WARNING - Connection pool is full, discarding connection: testphp.vulnweb.com. Connection pool size: 10
2025-06-03 01:42:12,317 - INFO - ====== WebDriver manager ======
2025-06-03 01:42:14,712 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:42:15,063 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:42:15,425 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:43:32,317 - ERROR - خطأ في التقاط الصور المتقدمة: Message: unknown error: unable to discover open window in chrome
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF699640AA0]
	(No symbol) [0x00007FF6996F5441]
	(No symbol) [0x00007FF6996F48B0]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-03 01:43:40,236 - INFO - ====== WebDriver manager ======
2025-06-03 01:43:42,456 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:43:44,817 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:43:45,081 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 01:43:51,780 - ERROR - خطأ في التقاط الصور المتقدمة: Message: unknown error: unable to discover open window in chrome
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF699640AA0]
	(No symbol) [0x00007FF6996F5441]
	(No symbol) [0x00007FF6996F48B0]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-03 01:43:55,846 - ERROR - خطأ في فحص CSRF: 'AdvancedBehavioralScanner' object has no attribute '_test_csrf_tokens_in_forms'
2025-06-03 01:43:55,846 - ERROR - خطأ في تحليل ملفات JavaScript: 'AdvancedBehavioralScanner' object has no attribute '_extract_javascript_urls'
2025-06-03 01:43:55,846 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_json_api_vulnerabilities'
2025-06-03 01:44:37,307 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_unsafe_default_settings'
2025-06-03 01:44:49,392 - ERROR - خطأ في اختبار الأنماط السلوكية المتقدم: 'AdvancedBehavioralScanner' object has no attribute '_infer_ui_logic_with_ai'
2025-06-03 01:45:33,028 - INFO - ====== WebDriver manager ======
2025-06-03 01:45:35,075 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:45:35,341 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 01:45:35,696 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
