2025-06-02 23:33:23,743 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:23,758 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:23,790 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:23,881 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:23,894 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:23,961 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,421 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,438 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,454 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,888 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,906 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:28,971 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:29,020 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:29,122 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:29,140 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:29,214 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:29,371 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:31,521 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /w3css/tryit.asp?filename=tryw3css_ref_badges
2025-06-02 23:33:36,142 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/python_lists_add.asp
2025-06-02 23:33:37,734 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /angular/angular_ref_directives.asp
2025-06-02 23:33:39,877 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/python_ml_train_test.asp
2025-06-02 23:33:43,003 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:33:43,888 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /mysql/mysql_groupby.asp
2025-06-02 23:33:45,502 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/python_ref_set.asp
2025-06-02 23:33:49,335 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_variables_identifiers1
2025-06-02 23:33:49,885 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_variables_multiple1
2025-06-02 23:33:49,885 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_algorithms1
2025-06-02 23:33:56,300 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /colors/colors_mixer.asp?colorbottom=B22222&colortop=FFFFFF
2025-06-02 23:33:58,135 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/python_primitivetypes.asp
2025-06-02 23:33:58,939 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/pandas/exercise.asp?x=xrcise_cleaning_empty_cells1
2025-06-02 23:33:58,941 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /mysql/trymysql.asp?filename=trysql_op_in
2025-06-02 23:34:00,701 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/trypython.asp?filename=demo_tuple_loop
2025-06-02 23:34:00,722 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/sql_server.asp
2025-06-02 23:34:00,809 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/showpython.asp?filename=demo_mysql_inner_join
2025-06-02 23:34:00,966 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:00,998 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:01,068 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:01,079 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:01,201 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:02,135 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/trysql.asp?filename=trysql_op_greater_than
2025-06-02 23:34:04,898 - WARNING - Some characters could not be decoded, and were replaced with REPLACEMENT CHARACTER.
2025-06-02 23:34:05,083 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/ref_keyword_and.asp
2025-06-02 23:34:05,607 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:05,623 - WARNING - Some characters could not be decoded, and were replaced with REPLACEMENT CHARACTER.
2025-06-02 23:34:05,635 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:05,748 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:05,884 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:05,932 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:05,999 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /git/git_exercises.asp
2025-06-02 23:34:06,141 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/func_sqlserver_datefromparts.asp
2025-06-02 23:34:06,318 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,438 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,771 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,773 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,824 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,834 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:06,839 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,150 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,267 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,550 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /colors/colors_mixer.asp?colorbottom=483D8B&colortop=FFFFFF
2025-06-02 23:34:07,617 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,619 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,750 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,767 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:07,802 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:08,210 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/func_sqlserver_radians.asp
2025-06-02 23:34:09,338 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /charsets/ref_emoji.asp
2025-06-02 23:34:09,519 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /plus/index.php
2025-06-02 23:34:09,784 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/exercise.asp?x=xrcise_strings_modify1
2025-06-02 23:34:09,959 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/exercise.asp?x=xrcise_modules1
2025-06-02 23:34:11,966 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_strings_length1
2025-06-02 23:34:14,984 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,085 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,216 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,217 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,257 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,430 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,447 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,461 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,581 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,582 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,584 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,797 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,966 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:15,968 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:17,115 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:17,184 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:17,184 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:17,311 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:18,182 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/cpp_getstarted.asp
2025-06-02 23:34:18,732 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/cpp_conditions_reallife.asp
2025-06-02 23:34:19,832 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/trypython.asp?filename=demo_list_comprehension_hello
2025-06-02 23:34:20,261 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_data_types_numeric1
2025-06-02 23:34:21,663 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /tags/tag_div.asp
2025-06-02 23:34:21,734 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:21,780 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:21,867 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:21,869 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:21,883 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,152 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /react/react_es6_array_methods.asp
2025-06-02 23:34:22,186 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,315 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,332 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,333 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,349 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,366 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,433 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,548 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,632 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,832 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,880 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,900 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,900 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:22,998 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:23,015 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:23,035 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/exercise.asp?x=xrcise_count1
2025-06-02 23:34:23,100 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:23,166 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:24,364 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/trycpp.asp?filename=demo_stack_size
2025-06-02 23:34:24,547 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:24,665 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /plus/index.php
2025-06-02 23:34:24,797 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,000 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,016 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,147 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,197 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,198 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,312 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,312 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:25,548 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:26,164 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/trypython.asp?filename=demo_matplotlib_scatter_cmap_OrRd
2025-06-02 23:34:26,289 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:26,383 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:26,383 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:26,564 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:26,916 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /lib/pathfinder/main.css?v=1.0.7
2025-06-02 23:34:27,031 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:27,033 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:27,062 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:27,211 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:28,434 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /sql/func_sqlserver_quotename.asp
2025-06-02 23:34:28,965 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:28,966 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,095 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,215 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,367 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,596 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,835 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,864 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,932 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,934 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:29,934 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:30,034 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:30,035 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /howto/howto_js_sort_numeric_array.asp
2025-06-02 23:34:31,911 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:31,930 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:31,932 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:31,966 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:31,980 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:31,983 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:32,015 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:32,018 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:32,312 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/cpp_getstarted.asp
2025-06-02 23:34:32,594 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /python/trypython.asp?filename=demo_list_comprehension_hello
2025-06-02 23:34:34,229 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_arrays1
2025-06-02 23:34:43,460 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /charsets/ref_utf_math.asp
2025-06-02 23:34:43,561 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:43,581 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:44,051 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:44,246 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:44,661 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:47,729 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /w3js/w3js_selectors.asp
2025-06-02 23:34:48,645 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:48,862 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:48,912 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:48,928 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:49,002 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:49,002 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:51,278 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:51,375 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:51,387 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:51,391 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /howto/howto_js_sort_numeric_array.asp
2025-06-02 23:34:52,660 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:52,694 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:52,729 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:52,827 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:53,012 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:53,210 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,476 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,476 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,529 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,562 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,681 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,683 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,695 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,725 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,725 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,726 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,732 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,744 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,776 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,882 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,894 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,895 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,895 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,921 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,927 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,928 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,930 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:56,934 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:57,011 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:34:57,413 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /css/css_study_plan.asp
2025-06-02 23:34:59,721 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cpp/exercise.asp?x=xrcise_operators_logical1
2025-06-02 23:35:00,232 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,244 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,244 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,626 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,630 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,644 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,645 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,792 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,794 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,873 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:00,896 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,094 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,275 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,362 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,371 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,495 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,496 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,511 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,511 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,626 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,679 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,713 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,769 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,878 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:01,910 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,609 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,628 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,794 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,795 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,811 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,813 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:02,977 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,027 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,177 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,554 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,558 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,561 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,643 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:03,694 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:04,175 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:04,175 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:04,412 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:04,944 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:04,960 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,111 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,127 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,496 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,709 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /mysql/func_mysql_mod.asp
2025-06-02 23:35:05,890 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,890 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:05,959 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:06,046 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:06,280 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:06,293 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /howto/howto_js_sort_numeric_array.asp
2025-06-02 23:35:07,245 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:07,277 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:07,376 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:08,426 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:09,131 - WARNING - Connection pool is full, discarding connection: www.w3schools.com. Connection pool size: 10
2025-06-02 23:35:15,616 - WARNING - Some characters could not be decoded, and were replaced with REPLACEMENT CHARACTER.
2025-06-02 23:35:31,789 - WARNING - Some characters could not be decoded, and were replaced with REPLACEMENT CHARACTER.
2025-06-02 23:35:50,261 - WARNING - Some characters could not be decoded, and were replaced with REPLACEMENT CHARACTER.
2025-06-02 23:41:29,230 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-02 23:41:29,240 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-02 23:42:44,926 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_detailed_error_messages'
2025-06-02 23:42:55,427 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_debug_features_exposure'
2025-06-02 23:44:35,745 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-02 23:44:35,751 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-02 23:45:19,893 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_detailed_error_messages'
2025-06-02 23:45:22,351 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_debug_features_exposure'
2025-06-02 23:46:16,491 - ERROR - خطأ في تطبيق K-Means: name 'KMeans' is not defined
2025-06-02 23:47:25,391 - ERROR - خطأ في التقاط الصور المتقدمة: Message: timeout: Timed out receiving message from renderer: 15.000
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF6996564EC]
	(No symbol) [0x00007FF6996561DA]
	(No symbol) [0x00007FF699653E8A]
	(No symbol) [0x00007FF69965483F]
	(No symbol) [0x00007FF6996633AE]
	(No symbol) [0x00007FF6996797E1]
	(No symbol) [0x00007FF69968091A]
	(No symbol) [0x00007FF699654FAD]
	(No symbol) [0x00007FF6996794D0]
	(No symbol) [0x00007FF69970F276]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-02 23:48:14,786 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-02 23:48:14,930 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-02 23:49:00,122 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_detailed_error_messages'
2025-06-02 23:49:02,502 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_debug_features_exposure'
2025-06-02 23:49:37,223 - ERROR - خطأ في تطبيق K-Means: name 'KMeans' is not defined
2025-06-02 23:50:21,392 - ERROR - خطأ في التقاط الصور المتقدمة: Message: timeout: Timed out receiving message from renderer: 8.000
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF69989CF45+75717]
	GetHandleVerifier [0x00007FF69989CFA0+75808]
	(No symbol) [0x00007FF699668F9A]
	(No symbol) [0x00007FF6996564EC]
	(No symbol) [0x00007FF6996561DA]
	(No symbol) [0x00007FF699653E8A]
	(No symbol) [0x00007FF69965483F]
	(No symbol) [0x00007FF6996633AE]
	(No symbol) [0x00007FF6996797E1]
	(No symbol) [0x00007FF69968091A]
	(No symbol) [0x00007FF699654FAD]
	(No symbol) [0x00007FF6996794D0]
	(No symbol) [0x00007FF69970F276]
	(No symbol) [0x00007FF6996E7153]
	(No symbol) [0x00007FF6996B0421]
	(No symbol) [0x00007FF6996B11B3]
	GetHandleVerifier [0x00007FF699B9D71D+3223453]
	GetHandleVerifier [0x00007FF699B97CC2+3200322]
	GetHandleVerifier [0x00007FF699BB5AF3+3322739]
	GetHandleVerifier [0x00007FF6998B6A1A+180890]
	GetHandleVerifier [0x00007FF6998BE11F+211359]
	GetHandleVerifier [0x00007FF6998A5294+109332]
	GetHandleVerifier [0x00007FF6998A5442+109762]
	GetHandleVerifier [0x00007FF69988BA59+4825]
	BaseThreadInitThunk [0x00007FFB1D47E8D7+23]
	RtlUserThreadStart [0x00007FFB1E71C5DC+44]

2025-06-02 23:51:56,504 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_race_conditions_in_reset'
2025-06-02 23:51:56,512 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-02 23:54:33,645 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_authentication_logic_bypass'
2025-06-02 23:54:33,654 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_redirect_uri_vulnerabilities'
2025-06-02 23:54:51,230 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.w3schools.com', port=443): Read timed out. (read timeout=10)")': /cp
2025-06-02 23:55:35,284 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-02 23:55:38,584 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
2025-06-02 23:57:16,948 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_authentication_logic_bypass'
2025-06-02 23:57:17,017 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_redirect_uri_vulnerabilities'
2025-06-02 23:57:54,564 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-02 23:57:57,095 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
2025-06-02 23:59:39,291 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_authentication_logic_bypass'
2025-06-02 23:59:39,300 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_redirect_uri_vulnerabilities'
2025-06-03 00:14:07,241 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_special_authentication_conditions'
2025-06-03 00:14:07,256 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_state_vulnerabilities'
2025-06-03 00:14:49,596 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_unsafe_default_settings'
2025-06-03 00:23:21,667 - ERROR - خطأ في اختبار تجاوز المصادقة: 'AdvancedBehavioralScanner' object has no attribute '_test_authentication_logic_bypass'
2025-06-03 00:23:21,679 - ERROR - خطأ في اختبار OAuth: 'AdvancedBehavioralScanner' object has no attribute '_test_oauth_common_vulnerabilities'
2025-06-03 00:24:06,849 - ERROR - خطأ في فحص التهيئة الخاطئة: 'AdvancedBehavioralScanner' object has no attribute '_test_system_information_disclosure'
2025-06-03 00:24:09,779 - ERROR - خطأ في فحص منطق الأعمال: 'AdvancedBehavioralScanner' object has no attribute '_test_comprehensive_registration_flaws'
