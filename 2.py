import os
import shutil

def delete_augment_ai_traces():
    base_paths = [
        os.path.join(os.environ["APPDATA"], "Code", "User", "globalStorage"),
        os.path.join(os.environ["APPDATA"], "Code", "User", "workspaceStorage"),
        os.path.join(os.environ["APPDATA"], "Code", "User", "storage.json"),
        os.path.join(os.environ["APPDATA"], "Code", "logs"),
    ]

    count = 0

    for path in base_paths:
        if os.path.isdir(path):
            for root, dirs, files in os.walk(path):
                for name in dirs + files:
                    if "augment" in name.lower():
                        full_path = os.path.join(root, name)
                        try:
                            if os.path.isdir(full_path):
                                shutil.rmtree(full_path)
                            else:
                                os.remove(full_path)
                            print(f"[+] حذف: {full_path}")
                            count += 1
                        except Exception as e:
                            print(f"[!] فشل في حذف {full_path}: {e}")
        elif os.path.isfile(path) and "augment" in path.lower():
            try:
                os.remove(path)
                print(f"[+] حذف: {path}")
                count += 1
            except Exception as e:
                print(f"[!] فشل في حذف {path}: {e}")

    print(f"\n✅ تم حذف {count} من آثار Augment AI بنجاح.\n")
    print("🔁 أعد تشغيل Visual Studio Code، ثم قم بتسجيل الدخول بحساب جديد.")

if __name__ == "__main__":
    delete_augment_ai_traces()
